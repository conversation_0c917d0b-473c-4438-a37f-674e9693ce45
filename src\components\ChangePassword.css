/* Change Password Component Styles */

.change-password-container {
  background: white;
  border-radius: 0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.change-password-success {
  background: white;
  border-radius: 0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  padding: 1.5rem;
}

.change-password-success-content {
  text-align: center;
}

.change-password-success-icon {
  margin: 0 auto;
  height: 3rem;
  width: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #dcfce7;
  margin-bottom: 1rem;
}

.change-password-success-icon svg {
  height: 1.5rem;
  width: 1.5rem;
  color: #16a34a;
}

.change-password-success-title {
  font-size: 1.125rem;
  font-weight: 500;
  color: #111827;
  margin-bottom: 0.5rem;
  font-family: 'Battambang', sans-serif;
}

.change-password-success-text {
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 1rem;
}

.change-password-success-button {
  padding: 0.5rem 1rem;
  background: #2563eb;
  color: white;
  border-radius: 0;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: 'Source Sans Pro', sans-serif;
  border: none;
}

.change-password-success-button:hover {
  background: #1d4ed8;
}

.change-password-header {
  border-bottom: 1px solid #e5e7eb;
  padding: 1.5rem;
}

.change-password-header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.change-password-title {
  font-size: 1.125rem;
  font-weight: 500;
  color: #111827;
  font-family: 'Battambang', sans-serif;
}

.change-password-close {
  color: #9ca3af;
  cursor: pointer;
  transition: color 0.2s ease;
}

.change-password-close:hover {
  color: #6b7280;
}

.change-password-close svg {
  height: 1.5rem;
  width: 1.5rem;
}

.change-password-form {
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.change-password-form-group {
  display: flex;
  flex-direction: column;
}

.change-password-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
  font-family: 'Source Sans Pro', sans-serif;
}

.change-password-input-wrapper {
  position: relative;
}

.change-password-input {
  width: 100%;
  padding: 0.5rem 0.75rem;
  padding-right: 2.5rem;
  border: 1px solid #d1d5db;
  border-radius: 0;
  font-family: 'Source Sans Pro', sans-serif;
  background: white;
  color: #374151;
}

.change-password-input:focus {
  outline: none;
  border-color: #d4af37;
  box-shadow: 0 0 0 2px rgba(212, 175, 55, 0.2);
}

.change-password-input:disabled {
  background: #f9fafb;
  color: #6b7280;
  cursor: not-allowed;
}

.change-password-input::placeholder {
  color: #9ca3af;
}

/* Password Strength */
.change-password-strength {
  margin-top: 0.5rem;
}

.change-password-strength-bar-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.change-password-strength-bar {
  flex: 1;
  background: #e5e7eb;
  border-radius: 9999px;
  height: 0.5rem;
}

.change-password-strength-fill {
  height: 0.5rem;
  border-radius: 9999px;
  transition: all 0.3s ease;
}

.change-password-strength-fill.weak {
  background: #ef4444;
}

.change-password-strength-fill.fair {
  background: #eab308;
}

.change-password-strength-fill.good {
  background: #3b82f6;
}

.change-password-strength-fill.strong {
  background: #22c55e;
}

.change-password-strength-text {
  font-size: 0.75rem;
  font-weight: 500;
}

.change-password-strength-text.weak {
  color: #dc2626;
}

.change-password-strength-text.fair {
  color: #ca8a04;
}

.change-password-strength-text.good {
  color: #2563eb;
}

.change-password-strength-text.strong {
  color: #16a34a;
}

.change-password-feedback {
  margin-top: 0.25rem;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.change-password-feedback-item {
  font-size: 0.75rem;
  color: #6b7280;
}

.change-password-feedback-item::before {
  content: "• ";
}

/* Password Mismatch */
.change-password-mismatch {
  margin-top: 0.25rem;
  font-size: 0.75rem;
  color: #dc2626;
}

/* Checkbox */
.change-password-checkbox-group {
  display: flex;
  align-items: center;
}

.change-password-checkbox {
  border-radius: 0;
  border: 1px solid #d1d5db;
  color: #2563eb;
}

.change-password-checkbox:focus {
  box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.2);
}

.change-password-checkbox-label {
  margin-left: 0.5rem;
  font-size: 0.875rem;
  color: #374151;
}

/* Actions */
.change-password-actions {
  display: flex;
  gap: 1rem;
}

.change-password-button {
  flex: 1;
  padding: 0.5rem 1rem;
  border-radius: 0;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: 'Source Sans Pro', sans-serif;
  font-weight: 500;
  border: none;
}

.change-password-button.cancel {
  font-size: 0.875rem;
  color: #374151;
  background: #f3f4f6;
}

.change-password-button.cancel:hover:not(:disabled) {
  background: #e5e7eb;
}

.change-password-button.submit {
  background: #2563eb;
  color: white;
  font-weight: 500;
}

.change-password-button.submit:hover:not(:disabled) {
  background: #1d4ed8;
}

.change-password-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.change-password-button-content {
  display: flex;
  align-items: center;
  justify-content: center;
}

.change-password-button-text {
  margin-left: 0.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .change-password-header {
    padding: 1rem;
  }
  
  .change-password-form {
    padding: 1rem;
  }
  
  .change-password-actions {
    flex-direction: column;
  }
  
  .change-password-header-content {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
}
